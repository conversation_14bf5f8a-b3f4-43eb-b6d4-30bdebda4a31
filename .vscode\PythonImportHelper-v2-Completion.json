[{"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "Pipeline", "importPath": "src.core.pipeline", "description": "src.core.pipeline", "isExtraImport": true, "detail": "src.core.pipeline", "documentation": {}}, {"label": "Pipeline", "importPath": "src.core.pipeline", "description": "src.core.pipeline", "isExtraImport": true, "detail": "src.core.pipeline", "documentation": {}}, {"label": "Pipeline", "importPath": "src.core.pipeline", "description": "src.core.pipeline", "isExtraImport": true, "detail": "src.core.pipeline", "documentation": {}}, {"label": "Pipeline", "importPath": "src.core.pipeline", "description": "src.core.pipeline", "isExtraImport": true, "detail": "src.core.pipeline", "documentation": {}}, {"label": "Neo4jClient", "importPath": "src.core.neo4j_client", "description": "src.core.neo4j_client", "isExtraImport": true, "detail": "src.core.neo4j_client", "documentation": {}}, {"label": "Neo4jClient", "importPath": "src.core.neo4j_client", "description": "src.core.neo4j_client", "isExtraImport": true, "detail": "src.core.neo4j_client", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "src.data.whitelist_loader", "description": "src.data.whitelist_loader", "isExtraImport": true, "detail": "src.data.whitelist_loader", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Set", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Set", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Generator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Generator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Set", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Set", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "settings", "importPath": "src.config.settings", "description": "src.config.settings", "isExtraImport": true, "detail": "src.config.settings", "documentation": {}}, {"label": "load_whitelists", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "llm_extract", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "whitelist_filter", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "get_neo4j_driver", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "batch_write_to_neo4j", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "scripts.csv_to_neo4j", "description": "scripts.csv_to_neo4j", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    if len(sys.argv) < 2:\n        print(\"用法:\")\n        print(\"  python scripts/csv_to_neo4j.py <CSV文件路径> [--clear]\")\n        print(\"  python scripts/csv_to_neo4j.py verify\")\n        print(\"\")\n        print(\"参数:\")\n        print(\"  --clear    导入前清空数据库\")\n        print(\"  verify     验证数据库状态\")", "detail": "scripts.csv_to_neo4j", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "scripts.extract_to_csv", "description": "scripts.extract_to_csv", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    if len(sys.argv) < 2:\n        print(\"用法:\")\n        print(\"  python scripts/extract_to_csv.py <文本文件路径> [输出CSV文件名]\")\n        print(\"  python scripts/extract_to_csv.py analyze <CSV文件路径>\")\n        print(\"\")\n        print(\"示例:\")\n        print(\"  python scripts/extract_to_csv.py data/samples/python第八章.txt\")\n        print(\"  python scripts/extract_to_csv.py data/samples/python第八章.txt my_output.csv\")", "detail": "scripts.extract_to_csv", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "scripts.pipeline", "description": "scripts.pipeline", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    if len(sys.argv) < 2:\n        print(\"用法:\")\n        print(\"  python scripts/pipeline.py <文本文件路径> [--clear]\")\n        print(\"\")\n        print(\"参数:\")\n        print(\"  --clear    处理前清空数据库\")\n        print(\"\")\n        print(\"示例:\")", "detail": "scripts.pipeline", "documentation": {}}, {"label": "setup_constraints", "kind": 2, "importPath": "scripts.setup_constraints", "description": "scripts.setup_constraints", "peekOfCode": "def setup_constraints():\n    \"\"\"设置Neo4j约束\"\"\"\n    print(\"开始设置Neo4j约束...\")\n    # 创建客户端\n    neo4j_client = Neo4jClient()\n    whitelist_loader = WhitelistLoader()\n    # 测试连接\n    if not neo4j_client.test_connection():\n        print(\"错误: Neo4j连接失败\")\n        return False", "detail": "scripts.setup_constraints", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "scripts.setup_constraints", "description": "scripts.setup_constraints", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    if len(sys.argv) > 1 and sys.argv[1] in [\"-h\", \"--help\"]:\n        print(\"用法:\")\n        print(\"  python scripts/setup_constraints.py\")\n        print(\"\")\n        print(\"说明:\")\n        print(\"  为Neo4j数据库中的所有标签设置必要的约束\")\n        print(\"  包括name和description字段的存在性约束\")\n        print(\"  为重要标签(Function, Concept, Parameter)设置name字段的唯一性约束\")", "detail": "scripts.setup_constraints", "documentation": {}}, {"label": "PromptTemplates", "kind": 6, "importPath": "src.config.prompts", "description": "src.config.prompts", "peekOfCode": "class PromptTemplates:\n    \"\"\"提示词模板管理类\"\"\"\n    @staticmethod\n    def create_system_prompt(entity_whitelist: Set[str], \n                           label_whitelist: Set[str], \n                           relation_whitelist: Set[str]) -> str:\n        \"\"\"\n        创建系统提示词\n        Args:\n            entity_whitelist: 实体白名单", "detail": "src.config.prompts", "documentation": {}}, {"label": "Settings", "kind": 6, "importPath": "src.config.settings", "description": "src.config.settings", "peekOfCode": "class Settings:\n    \"\"\"配置管理类\"\"\"\n    def __init__(self):\n        \"\"\"初始化配置\"\"\"\n        # 加载环境变量\n        load_dotenv(override=True)\n        # Neo4j配置\n        self.neo4j_uri = os.getenv(\"NEO4J_URI\", \"bolt://localhost:7687\")\n        self.neo4j_user = os.getenv(\"NEO4J_USER\", \"neo4j\")\n        self.neo4j_password = os.getenv(\"NEO4J_PASSWORD\")", "detail": "src.config.settings", "documentation": {}}, {"label": "settings", "kind": 5, "importPath": "src.config.settings", "description": "src.config.settings", "peekOfCode": "settings = Settings()", "detail": "src.config.settings", "documentation": {}}, {"label": "LLMClient", "kind": 6, "importPath": "src.core.llm_client", "description": "src.core.llm_client", "peekOfCode": "class LLMClient:\n    \"\"\"LLM客户端\"\"\"\n    def __init__(self):\n        \"\"\"初始化LLM客户端\"\"\"\n        self.client = OpenAI(api_key=settings.openai_api_key)\n        self.model = settings.llm_model\n        self.prompt_templates = PromptTemplates()\n    def extract_triples(self, text_chunk: str, \n                       entity_whitelist: Set[str],\n                       label_whitelist: Set[str], ", "detail": "src.core.llm_client", "documentation": {}}, {"label": "Neo4jClient", "kind": 6, "importPath": "src.core.neo4j_client", "description": "src.core.neo4j_client", "peekOfCode": "class Neo4jClient:\n    \"\"\"Neo4j客户端\"\"\"\n    def __init__(self):\n        \"\"\"初始化Neo4j客户端\"\"\"\n        self.driver = None\n        self._connect()\n    def _connect(self):\n        \"\"\"建立Neo4j连接\"\"\"\n        try:\n            self.driver = GraphDatabase.driver(", "detail": "src.core.neo4j_client", "documentation": {}}, {"label": "Pipeline", "kind": 6, "importPath": "src.core.pipeline", "description": "src.core.pipeline", "peekOfCode": "class Pipeline:\n    \"\"\"知识图谱构建管道\"\"\"\n    def __init__(self):\n        \"\"\"初始化管道\"\"\"\n        self.text_processor = TextProcessor()\n        self.llm_client = LLMClient()\n        self.neo4j_client = Neo4jClient()\n        self.whitelist_loader = WhitelistLoader()\n        self.csv_handler = CSVHandler()\n        # 加载白名单", "detail": "src.core.pipeline", "documentation": {}}, {"label": "TextProcessor", "kind": 6, "importPath": "src.core.text_processor", "description": "src.core.text_processor", "peekOfCode": "class TextProcessor:\n    \"\"\"文本处理器\"\"\"\n    def __init__(self, max_chars: int = None):\n        \"\"\"\n        初始化文本处理器\n        Args:\n            max_chars: 每块最大字符数，默认使用配置值\n        \"\"\"\n        self.max_chars = max_chars or settings.max_chunk_chars\n    def yield_chunks(self, text: str) -> Generator[str, None, None]:", "detail": "src.core.text_processor", "documentation": {}}, {"label": "CSVHandler", "kind": 6, "importPath": "src.data.csv_handler", "description": "src.data.csv_handler", "peekOfCode": "class CSVHandler:\n    \"\"\"CSV处理器\"\"\"\n    def __init__(self):\n        \"\"\"初始化CSV处理器\"\"\"\n        pass\n    def save_triples(self, triples: List[Dict[str, str]], filename: str = \"extracted_triples.csv\") -> str:\n        \"\"\"\n        保存三元组到CSV文件\n        Args:\n            triples: 三元组列表", "detail": "src.data.csv_handler", "documentation": {}}, {"label": "DataFilter", "kind": 6, "importPath": "src.data.filter", "description": "src.data.filter", "peekOfCode": "class DataFilter:\n    \"\"\"数据过滤器\"\"\"\n    def __init__(self, entity_whitelist: Set[str], \n                 label_whitelist: Set[str], \n                 relation_whitelist: Set[str]):\n        \"\"\"\n        初始化数据过滤器\n        Args:\n            entity_whitelist: 实体白名单\n            label_whitelist: 标签白名单", "detail": "src.data.filter", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 6, "importPath": "src.data.whitelist_loader", "description": "src.data.whitelist_loader", "peekOfCode": "class WhitelistLoader:\n    \"\"\"白名单加载器\"\"\"\n    def __init__(self):\n        \"\"\"初始化白名单加载器\"\"\"\n        self._entity_whitelist = None\n        self._label_whitelist = None\n        self._relation_whitelist = None\n    def load_entities(self) -> Set[str]:\n        \"\"\"\n        加载实体白名单", "detail": "src.data.whitelist_loader", "documentation": {}}, {"label": "validate_file_path", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def validate_file_path(filepath: str) -> bool:\n    \"\"\"\n    验证文件路径是否存在\n    Args:\n        filepath: 文件路径\n    Returns:\n        bool: 文件是否存在\n    \"\"\"\n    return os.path.exists(filepath) and os.path.isfile(filepath)\ndef safe_read_file(filepath: str, encoding: str = \"utf-8\") -> Optional[str]:", "detail": "src.utils.helpers", "documentation": {}}, {"label": "safe_read_file", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def safe_read_file(filepath: str, encoding: str = \"utf-8\") -> Optional[str]:\n    \"\"\"\n    安全读取文件内容\n    Args:\n        filepath: 文件路径\n        encoding: 文件编码\n    Returns:\n        Optional[str]: 文件内容，失败时返回None\n    \"\"\"\n    try:", "detail": "src.utils.helpers", "documentation": {}}, {"label": "ensure_directory", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def ensure_directory(dirpath: str) -> bool:\n    \"\"\"\n    确保目录存在，不存在则创建\n    Args:\n        dirpath: 目录路径\n    Returns:\n        bool: 目录是否存在或创建成功\n    \"\"\"\n    try:\n        if not os.path.exists(dirpath):", "detail": "src.utils.helpers", "documentation": {}}, {"label": "format_file_size", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def format_file_size(size_bytes: int) -> str:\n    \"\"\"\n    格式化文件大小\n    Args:\n        size_bytes: 字节数\n    Returns:\n        str: 格式化的大小字符串\n    \"\"\"\n    if size_bytes < 1024:\n        return f\"{size_bytes} B\"", "detail": "src.utils.helpers", "documentation": {}}, {"label": "get_file_info", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def get_file_info(filepath: str) -> dict:\n    \"\"\"\n    获取文件信息\n    Args:\n        filepath: 文件路径\n    Returns:\n        dict: 文件信息\n    \"\"\"\n    try:\n        stat = os.stat(filepath)", "detail": "src.utils.helpers", "documentation": {}}, {"label": "truncate_text", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def truncate_text(text: str, max_length: int = 100, suffix: str = \"...\") -> str:\n    \"\"\"\n    截断文本\n    Args:\n        text: 原始文本\n        max_length: 最大长度\n        suffix: 后缀\n    Returns:\n        str: 截断后的文本\n    \"\"\"", "detail": "src.utils.helpers", "documentation": {}}, {"label": "print_progress", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def print_progress(current: int, total: int, prefix: str = \"进度\"):\n    \"\"\"\n    打印进度信息\n    Args:\n        current: 当前进度\n        total: 总数\n        prefix: 前缀文本\n    \"\"\"\n    percentage = (current / total) * 100 if total > 0 else 0\n    print(f\"{prefix}: {current}/{total} ({percentage:.1f}%)\")", "detail": "src.utils.helpers", "documentation": {}}, {"label": "safe_divide", "kind": 2, "importPath": "src.utils.helpers", "description": "src.utils.helpers", "peekOfCode": "def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:\n    \"\"\"\n    安全除法，避免除零错误\n    Args:\n        numerator: 分子\n        denominator: 分母\n        default: 默认值\n    Returns:\n        float: 除法结果\n    \"\"\"", "detail": "src.utils.helpers", "documentation": {}}, {"label": "run_command", "kind": 2, "importPath": "tests.run_tests", "description": "tests.run_tests", "peekOfCode": "def run_command(cmd, description):\n    \"\"\"运行命令并显示结果\"\"\"\n    print(f\"\\n{'='*50}\")\n    print(f\"测试: {description}\")\n    print(f\"命令: {cmd}\")\n    print('='*50)\n    try:\n        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)\n        if result.stdout:\n            print(\"输出:\")", "detail": "tests.run_tests", "documentation": {}}, {"label": "check_prerequisites", "kind": 2, "importPath": "tests.run_tests", "description": "tests.run_tests", "peekOfCode": "def check_prerequisites():\n    \"\"\"检查前置条件\"\"\"\n    print(\"检查前置条件...\")\n    # 检查环境变量\n    required_vars = [\"OPENAI_API_KEY\", \"NEO4J_PASSWORD\"]\n    missing_vars = []\n    for var in required_vars:\n        if not os.getenv(var):\n            missing_vars.append(var)\n    if missing_vars:", "detail": "tests.run_tests", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "tests.run_tests", "description": "tests.run_tests", "peekOfCode": "def main():\n    \"\"\"主测试流程\"\"\"\n    print(\"=== Neo4j知识图谱系统综合测试 ===\")\n    # 检查前置条件\n    if not check_prerequisites():\n        print(\"前置条件检查失败，退出测试\")\n        return\n    test_results = []\n    # 测试1: 工具函数测试\n    success = run_command(\"python utils.py\", \"工具函数测试\")", "detail": "tests.run_tests", "documentation": {}}, {"label": "test_connection_detailed", "kind": 2, "importPath": "tests.test_neo4j_connection", "description": "tests.test_neo4j_connection", "peekOfCode": "def test_connection_detailed():\n    \"\"\"详细测试Neo4j连接\"\"\"\n    print(\"=== Neo4j连接详细测试 ===\")\n    # 1. 显示环境变量\n    print(\"\\n1. 环境变量检查:\")\n    print(f\"   NEO4J_URI: {settings.neo4j_uri}\")\n    print(f\"   NEO4J_USER: {settings.neo4j_user}\")\n    print(f\"   NEO4J_PASSWORD: {'*' * len(settings.neo4j_password) if settings.neo4j_password else 'NOT SET'}\")\n    try:\n        settings._validate_required_settings()", "detail": "tests.test_neo4j_connection", "documentation": {}}, {"label": "test_different_configs", "kind": 2, "importPath": "tests.test_neo4j_connection", "description": "tests.test_neo4j_connection", "peekOfCode": "def test_different_configs():\n    \"\"\"测试不同的连接配置\"\"\"\n    print(\"\\n=== 测试不同连接配置 ===\")\n    # 使用当前配置进行基本测试\n    print(\"\\n测试当前配置:\")\n    try:\n        neo4j_client = Neo4jClient()\n        if neo4j_client.test_connection():\n            print(\"✓ 当前配置 - 连接成功\")\n        else:", "detail": "tests.test_neo4j_connection", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "tests.test_neo4j_connection", "description": "tests.test_neo4j_connection", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"Neo4j连接诊断工具\")\n    print(\"=\" * 50)\n    # 主要连接测试\n    success = test_connection_detailed()\n    if not success:\n        print(\"\\n主要连接测试失败，尝试其他配置...\")\n        test_different_configs()\n    print(f\"\\n{'='*50}\")", "detail": "tests.test_neo4j_connection", "documentation": {}}, {"label": "import_csv_data", "kind": 2, "importPath": "tests.test_pipeline", "description": "tests.test_pipeline", "peekOfCode": "def import_csv_data():\n    \"\"\"直接导入CSV数据到Neo4j\"\"\"\n    print(\"=== 导入CSV数据到Neo4j ===\")\n    # 读取CSV文件\n    try:\n        import pandas as pd\n        df = pd.read_csv(\"extracted_triples.csv\")\n        print(f\"读取到 {len(df)} 个三元组\")\n    except Exception as e:\n        print(f\"读取CSV失败: {e}\")", "detail": "tests.test_pipeline", "documentation": {}}, {"label": "test_small_pipeline", "kind": 2, "importPath": "tests.test_pipeline", "description": "tests.test_pipeline", "peekOfCode": "def test_small_pipeline():\n    \"\"\"测试小量数据的完整流程\"\"\"\n    print(\"=== 测试知识图谱构建管道 ===\")\n    # 测试文本\n    test_text = \"\"\"\n    ## 8.1 定义函数\n    函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可调用该函数。\n    当需要在程序中多次执行同一项任务时，无须反复编写完成该任务的代码，只需要调用执行该任务的函数。\n    函数可以接受参数，参数是传递给函数的值。函数还可以返回值给调用者。\n    实参是调用函数时传递的实际值，形参是函数定义中的参数名。", "detail": "tests.test_pipeline", "documentation": {}}, {"label": "clear_test_data", "kind": 2, "importPath": "tests.test_pipeline", "description": "tests.test_pipeline", "peekOfCode": "def clear_test_data():\n    \"\"\"清理测试数据\"\"\"\n    try:\n        driver = get_neo4j_driver()\n        with driver.session() as session:\n            # 删除所有节点和关系\n            session.run(\"MATCH (n) DETACH DELETE n\")\n            print(\"✓ 测试数据已清理\")\n        driver.close()\n    except Exception as e:", "detail": "tests.test_pipeline", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "example", "description": "example", "peekOfCode": "def main():\n    \"\"\"主函数 - 演示系统使用\"\"\"\n    print(\"🚀 Neo4j 知识图谱构建系统 - 使用示例\")\n    print(\"=\" * 50)\n    # 创建管道实例\n    print(\"1. 初始化管道...\")\n    try:\n        pipeline = Pipeline()\n        print(\"✅ 管道初始化成功\")\n    except Exception as e:", "detail": "example", "documentation": {}}]