- ***

## 简要流程

1. **准备环境**（Neo4j 5.x + APOC、Python 3.10、`neo4j`/`openai` 等）。
2. **编好白名单**：`entities.csv`（仅包含实体名称）、`labels.csv`、`relations.csv`。
3. **在 Neo4j 建唯一 & 存在约束**，保证每个节点都有 `description`。
4. **Python 管道**：
   1. 分块（≈ 700 tokens）读取 TXT；
   2. 用 GPT-4o「JSON Mode + 函数调用」一次性抽取 `{head, head_label, head_desc, relation, tail, tail_label, tail_desc}`；
   3. 过滤掉不在实体白名单中的实体，但**label 和 description 由 LLM 自动生成**；
   4. 通过 `UNWIND … MERGE` 批量写入 Neo4j；
5. **Cypher 后置清洗**（双保险）。
6. **验证** 数据完整性与约束生效。
7. **常见坑 & 优化**。

---

## 1 环境准备

| 组件                                  | 版本 / 说明                                                                                                                                                   |
| ------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Neo4j Community 5.23+（含 APOC 插件） | 支持 `CREATE CONSTRAINT … EXISTS` 等语法 [neo4j.com](https://neo4j.com/docs/cypher-manual/current/constraints/?utm_source=chatgpt.com)                        |
| Neo4j Python Driver                   | `neo4j~=5.19`，官方驱动性能最佳 [neo4j.com](https://neo4j.com/docs/python-manual/current/performance/?utm_source=chatgpt.com)                                 |
| OpenAI Python SDK                     | `openai>=1.14`，支持 JSON-mode & 函数调用 [community.openai.com](https://community.openai.com/t/how-do-i-use-the-new-json-mode/475890?utm_source=chatgpt.com) |
| 其它库                                | `pandas tqdm python-dotenv`（便于数据清洗与进度条）                                                                                                           |

```
bash


CopyEdit
pip install neo4j openai pandas tqdm python-dotenv
```

> GPT-4o 单轮最大输出 4 k tokens，上下文可达 128 k+，足够容纳结构化 JSON [community.openai.com](https://community.openai.com/t/what-is-the-token-limit-of-the-new-version-gpt-4o/752528?utm_source=chatgpt.com)

---

## 2 白名单 CSV 文件

```
csvCopyEdit# whitelists/entities.csv（仅包含实体名称，label和description由LLM生成）
函数
代码块
函数体
实参
形参
位置实参
可选形参
顺序实参
关键字实参
返回值
列表

# whitelists/labels.csv
label
Person
Concept
Course
Function
Parameter
DataType

# whitelists/relations.csv
relation_type
TEACHES
PREREQUISITE_OF
HAS_PARAMETER
RETURNS
CONTAINS
...
```

---

## 3 图模式与约束

```
cypherCopyEdit// 唯一 name 约束
CREATE CONSTRAINT person_name_unique IF NOT EXISTS
FOR (p:Person) REQUIRE p.name IS UNIQUE;

// description 必填（社区版 5.x 允许）
CREATE CONSTRAINT person_desc_required IF NOT EXISTS
FOR (p:Person) REQUIRE p.description IS NOT NULL;
```

完整约束语法见官方手册 [neo4j.com](https://neo4j.com/docs/cypher-manual/current/constraints/managing-constraints/?utm_source=chatgpt.com)。

---

## 4 Python 抽取 & 写入管道

> 完整脚本见附录 `pipeline.py`，此处分段讲解。

### 4.1 加载白名单

```
pythonCopyEditimport pandas as pd, pathlib as pl
# 实体白名单只包含名称，无需label和description
ENT_WL = set(pd.read_csv("whitelists/entities.csv", header=None)[0])  # 第一列为实体名称
LAB_WL = set(pd.read_csv("whitelists/labels.csv")["label"])
REL_WL = set(pd.read_csv("whitelists/relations.csv")["relation_type"])
```

### 4.2 分块 TXT

经验表明 **600–800 tokens** 的 segment 在成本与上下文完整度之间最平衡 [community.openai.com](https://community.openai.com/t/using-gpt-4-api-to-semantically-chunk-documents/715689?page=2&utm_source=chatgpt.com)：

```
pythonCopyEditdef yield_chunks(txt, max_chars=2800):
    buf = []
    size = 0
    for line in txt.splitlines():
        size += len(line)
        buf.append(line)
        if size >= max_chars:
            yield "\n".join(buf); buf, size = [], 0
    if buf:
        yield "\n".join(buf)
```

### 4.3 GPT-4o JSON-Mode 抽取

```
pythonCopyEditfrom openai import OpenAI
client = OpenAI()

FUNC_DEF = {
  "name": "add_triples",
  "description": "Return extracted triples",
  "parameters": {
    "type": "object",
    "properties": {
      "triples": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "head": {"type": "string"},
            "head_label": {"type": "string"},
            "head_desc": {"type": "string"},
            "relation": {"type": "string"},
            "tail": {"type": "string"},
            "tail_label": {"type": "string"},
            "tail_desc": {"type": "string"}
          },
          "required": ["head","head_label","relation","tail","tail_label"]
        }
      }
    },
    "required": ["triples"]
  }
}

SYSTEM_PROMPT = f"""
You are an information extraction assistant.
Only extract entities that appear in this whitelist: {list(ENT_WL)}
For labels and relations, only use types from these allow-lists: {list(LAB_WL)} and {list(REL_WL)}
If an entity is not in the entity whitelist, omit it completely.
For each kept entity, automatically generate:
1. An appropriate label from the allowed label list
2. A concise Chinese description (≤20 words) based on the context
"""

def llm_extract(chunk):
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        response_format={"type": "json_object"},   # JSON Mode :contentReference[oaicite:6]{index=6}
        temperature=0,
        messages=[
          {"role":"system","content":SYSTEM_PROMPT},
          {"role":"user","content":chunk}
        ],
        functions=[FUNC_DEF],
        function_call={"name":"add_triples"}
    )
    return response.choices[0].message.function_call.arguments["triples"]
```

### 4.4 过滤 & 去重

```
pythonCopyEditdef whitelist(triples):
    for t in triples:
        # 只检查实体名称是否在白名单中，label和description由LLM生成
        if (t["head"] in ENT_WL and t["tail"] in ENT_WL and
            t["head_label"] in LAB_WL and t["tail_label"] in LAB_WL and
            t["relation"] in REL_WL):
            yield t
```

### 4.5 批量写入 Neo4j

`UNWIND … MERGE` 是驱动官方推荐的大批写入套路 [neo4j.com](https://neo4j.com/docs/python-manual/current/performance/?utm_source=chatgpt.com)：

```
pythonCopyEditfrom neo4j import GraphDatabase
driver = GraphDatabase.driver("bolt://localhost:7687", auth=("neo4j","pass"))

QUERY = """
UNWIND $rows AS row
  MERGE (h:`%s` {name: row.head})
    ON CREATE SET h.description = row.head_desc
  MERGE (t:`%s` {name: row.tail})
    ON CREATE SET t.description = row.tail_desc
  MERGE (h)-[r:%s]->(t)
"""

def batch_write(session, rows_df):
    for (hl, tl, rel), grp in rows_df.groupby(
            ["head_label","tail_label","relation"]):
        session.run(QUERY % (hl, tl, rel), rows=grp.to_dict("records"))

from tqdm import tqdm, itertools
with driver.session() as sess:
    for chunk in tqdm(yield_chunks(open("book.txt",encoding="utf8").read())):
        triples = list(whitelist(llm_extract(chunk)))
        if triples:
            import pandas as pd
            batch_write(sess, pd.DataFrame(triples))
```

---

## 5 Cypher 后置清洗（可选）

```
cypherCopyEdit// 白名单节点（注意：entities.csv没有header）
LOAD CSV FROM 'file:///whitelists/entities.csv' AS row
WITH collect(row[0]) AS names
MATCH (n) WHERE NOT n.name IN names DETACH DELETE n;

// 白名单关系
LOAD CSV WITH HEADERS FROM 'file:///whitelists/relations.csv' AS row
WITH collect(row.relation_type) AS rels
MATCH ()-[r]-() WHERE NOT type(r) IN rels DELETE r;
```

大量数据可用 `apoc.periodic.iterate` 分批执行 [neo4j.com](https://neo4j.com/apoc/4.4/overview/apoc.periodic/apoc.periodic.iterate/?utm_source=chatgpt.com)。

---

## 6 验证

```
cypherCopyEdit// 是否所有节点都有 description
MATCH (n) WHERE n.description IS NOT NULL RETURN count(n);

// 抽样查看
MATCH p=()-[r]->() RETURN p LIMIT 25;
```

---

## 7 常见坑 & 优化

| 症状                       | 解决办法                                                               |
| -------------------------- | ---------------------------------------------------------------------- |
| **LLM 误抽实体**           | 在提示词中明确实体白名单，只提取白名单中的实体                         |
| **Label 分配不当**         | 在提示词中提供 label 列表，要求 LLM 根据上下文选择合适的 label         |
| **Description 缺失或超长** | 在 `SYSTEM_PROMPT` 明确「≤20 words；基于上下文自动生成描述」           |
| **写入速度慢**             | 每批 1 k–5 k 行；在驱动层 `session.execute_write` 包装事务             |
| **内存暴涨**               | `yield_chunks` 保持输入流式，不加载整书                                |
| **约束报错**               | 确认 Neo4j ≥ 5.23；低版本社区不支持 property-existence，可改脚本层校验 |

---

## 8 附录：完整脚本模板

```
bashCopyEdit# 1. 复制本仓库
git clone https://github.com/yourorg/txt2neo4j-gpt4o.git
cd txt2neo4j-gpt4o
pip install -r requirements.txt

# 2. 设置环境变量
echo "OPENAI_API_KEY=sk-xxx" > .env
python pipeline.py book.txt
```

脚本包含：

- `pipeline.py` →  主逻辑
- `utils.py` →  分块 / LLM 调用 / 过滤函数
- `cy_constraints.cql` →  约束一次性执行脚本
- `cleaning.cql` →  后置白名单清洗

---

通过以上步骤，即可**完全抛弃 spaCy NER**，仅依赖 GPT-4o 强大的结构化输出能力，批量提取实体-关系并写入 Neo4j——同时确保：

1. 只提取白名单中的实体名称
2. 每个节点的 label 和 description 由 LLM 根据上下文自动生成
3. 所有 Label / Relation 类型都严格受控于你的白名单
