head,head_label,head_desc,relation,tail,tail_label,tail_desc
函数,Function,带名字的代码块,is_a_type_of,代码块,CodeBlock,用于完成具体工作的代码块
函数,Function,带名字的代码块,contains,函数体,CodeBlock,函数内部的代码部分
函数,Function,带名字的代码块,requires,形参,Parameter,函数完成工作所需的信息
形参,Parameter,函数完成工作所需的信息,results_from,实参,Parameter,在调用函数时传递给函数的信息
位置实参,Parameter,按顺序传递的实参,is_a_type_of,实参,Parameter,在调用函数时传递给函数的信息
关键字实参,Parameter,通过变量名和值传递的实参,is_a_type_of,实参,Parameter,在调用函数时传递给函数的信息
列表,DataType,存储多个值的集合,collaborates_with,实参,Parameter,在调用函数时传递给函数的信息
函数,Function,带名字的代码块,requires,位置实参,Parameter,按顺序提供的实参
函数,Function,带名字的代码块,requires,关键字实参,Parameter,名值对形式的实参
函数,Function,带名字的代码块,contains,函数体,CodeBlock,实现函数功能的代码块
函数体,CodeBlock,实现函数功能的代码块,results_from,形参,Parameter,函数定义中的参数
形参,Parameter,函数定义中的参数,is_a_type_of,可选形参,Parameter,可以省略的参数
形参,Parameter,函数定义中的参数,is_a_type_of,顺序实参,Parameter,按顺序提供的参数
形参,Parameter,函数定义中的参数,is_a_type_of,关键字实参,Parameter,名值对形式的参数
函数,Function,带名字的代码块,contains,形参,Parameter,函数定义中的参数
形参,Parameter,函数定义中的参数,requires,实参,Parameter,函数调用时传入的参数
实参,Parameter,函数调用时传入的参数,is_a_type_of,位置实参,Parameter,按位置传递的参数
实参,Parameter,函数调用时传入的参数,is_a_type_of,关键字实参,Parameter,按关键字传递的参数
函数,Function,带名字的代码块,results_from,返回值,Variable,函数返回的结果
可选形参,Parameter,函数参数可选项,is_a_type_of,形参,Parameter,函数定义中的参数
返回值,Variable,函数的输出结果,results_from,函数体,CodeBlock,函数内部的执行代码
列表,DataType,存储多个元素的集合,contains,函数,Function,可以处理的输入类型
函数,Function,带名字的代码块,requires,列表,DataType,函数需要的输入类型
函数,Function,带名字的代码块,creates,代码块,CodeBlock,用于完成具体工作的代码块
实参,Parameter,传递给函数的值,requires,形参,Parameter,函数定义中的参数
形参,Parameter,函数定义中的参数,is_a_type_of,可选形参,Parameter,可以选择性传递的参数
函数,Function,带名字的代码块,contains,函数体,CodeBlock,函数内部的代码块
函数,Function,带名字的代码块,collaborates_with,返回值,Variable,函数执行后返回的结果
函数,Function,带名字的代码块,part_of,代码块,CodeBlock,用于完成具体工作的代码块
函数,Function,带名字的代码块,creates,代码块,CodeBlock,用于完成具体工作的代码块
形参,Parameter,函数定义中的参数,contains,位置实参,Parameter,按顺序传递的参数
形参,Parameter,函数定义中的参数,contains,关键字实参,Parameter,以键值对形式传递的参数
函数,Function,带名字的代码块,results_from,返回值,Variable,函数执行后返回的结果
函数体,CodeBlock,函数内部的代码,part_of,函数,Function,带名字的代码块
函数,Function,带名字的代码块,requires,可选形参,Parameter,函数中可选的参数
函数,Function,带名字的代码块,is_a_type_of,代码块,CodeBlock,用于完成具体工作的代码块
函数,Function,带名字的代码块,contains,形参,Parameter,函数定义时的参数
函数,Function,带名字的代码块,results_from,返回值,Variable,函数执行后返回的结果
函数,Function,带名字的代码块,requires,可选形参,Parameter,函数调用时可选的参数
函数,Function,带名字的代码块,requires,顺序实参,Parameter,函数调用时按顺序传入的参数
函数,Function,带名字的代码块,requires,位置实参,Parameter,函数调用时按位置传入的参数
函数,Function,带名字的代码块,requires,关键字实参,Parameter,函数调用时以关键字传入的参数
函数,Function,带名字的代码块,is_a_type_of,代码块,CodeBlock,用于完成具体工作的代码块
实参,Parameter,传递给函数的值,requires,函数,Function,带名字的代码块
形参,Parameter,函数定义中的参数,depends_on,返回值,Variable,函数的输出结果
返回值,Variable,函数执行后的结果,results_from,函数,Function,带名字的代码块
位置实参,Parameter,按位置传递的参数,is_a_type_of,实参,Parameter,传递给函数的值
关键字实参,Parameter,按名称传递的参数,is_a_type_of,实参,Parameter,传递给函数的值
