#!/usr/bin/env python3
"""
抽取到CSV脚本

从文本文件抽取实体关系并保存到CSV文件。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.pipeline import Pipeline


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python scripts/extract_to_csv.py <文本文件路径> [输出CSV文件名]")
        print("  python scripts/extract_to_csv.py analyze <CSV文件路径>")
        print("")
        print("示例:")
        print("  python scripts/extract_to_csv.py data/samples/python第八章.txt")
        print("  python scripts/extract_to_csv.py data/samples/python第八章.txt my_output.csv")
        print("  python scripts/extract_to_csv.py analyze data/output/extracted_triples.csv")
        return
    
    # 创建管道
    pipeline = Pipeline()
    
    try:
        if sys.argv[1] == "analyze":
            # 分析模式
            if len(sys.argv) < 3:
                print("错误: 请提供要分析的CSV文件路径")
                return
            
            csv_path = sys.argv[2]
            print(f"分析CSV文件: {csv_path}")
            pipeline.analyze_csv(csv_path)
            
        else:
            # 抽取模式
            text_file = sys.argv[1]
            output_csv = sys.argv[2] if len(sys.argv) > 2 else "extracted_triples.csv"
            
            print(f"从文本文件抽取: {text_file}")
            print(f"输出CSV文件: {output_csv}")
            
            # 执行抽取
            result_path = pipeline.process_text_to_csv(text_file, output_csv)
            
            if result_path:
                print(f"\n✅ 抽取完成！")
                print(f"输出文件: {result_path}")
                print(f"\n可以使用以下命令分析结果:")
                print(f"python scripts/extract_to_csv.py analyze {result_path}")
            else:
                print("\n❌ 抽取失败！")
                
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n错误: {e}")
    finally:
        # 清理资源
        pipeline.cleanup()


if __name__ == "__main__":
    main()
