"""
数据过滤模块

负责根据白名单过滤三元组数据。
"""

from typing import List, Dict, Generator, Set


class DataFilter:
    """数据过滤器"""
    
    def __init__(self, entity_whitelist: Set[str], 
                 label_whitelist: Set[str], 
                 relation_whitelist: Set[str]):
        """
        初始化数据过滤器
        
        Args:
            entity_whitelist: 实体白名单
            label_whitelist: 标签白名单
            relation_whitelist: 关系白名单
        """
        self.entity_whitelist = entity_whitelist
        self.label_whitelist = label_whitelist
        self.relation_whitelist = relation_whitelist
    
    def filter_triples(self, triples: List[Dict[str, str]]) -> Generator[Dict[str, str], None, None]:
        """
        根据白名单过滤三元组
        
        Args:
            triples: 三元组列表
            
        Yields:
            Dict[str, str]: 过滤后的三元组
        """
        for triple in triples:
            if self._is_valid_triple(triple):
                # 确保描述字段存在
                if "head_desc" not in triple:
                    triple["head_desc"] = ""
                if "tail_desc" not in triple:
                    triple["tail_desc"] = ""
                
                yield triple
    
    def _is_valid_triple(self, triple: Dict[str, str]) -> bool:
        """
        检查三元组是否有效
        
        Args:
            triple: 三元组
            
        Returns:
            bool: 是否有效
        """
        # 检查必需字段
        required_fields = ["head", "head_label", "relation", "tail", "tail_label"]
        if not all(field in triple for field in required_fields):
            return False
        
        # 检查是否在白名单中
        return (
            triple["head"] in self.entity_whitelist and
            triple["tail"] in self.entity_whitelist and
            triple["head_label"] in self.label_whitelist and
            triple["tail_label"] in self.label_whitelist and
            triple["relation"] in self.relation_whitelist
        )
    
    def get_filter_stats(self, triples: List[Dict[str, str]]) -> Dict[str, int]:
        """
        获取过滤统计信息
        
        Args:
            triples: 原始三元组列表
            
        Returns:
            Dict[str, int]: 统计信息
        """
        total = len(triples)
        valid = sum(1 for triple in triples if self._is_valid_triple(triple))
        
        return {
            "total_triples": total,
            "valid_triples": valid,
            "filtered_out": total - valid,
            "pass_rate": valid / total if total > 0 else 0
        }
    
    def validate_triple_fields(self, triple: Dict[str, str]) -> List[str]:
        """
        验证三元组字段并返回错误信息
        
        Args:
            triple: 三元组
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        # 检查必需字段
        required_fields = ["head", "head_label", "relation", "tail", "tail_label"]
        for field in required_fields:
            if field not in triple:
                errors.append(f"缺少字段: {field}")
        
        if errors:
            return errors
        
        # 检查白名单
        if triple["head"] not in self.entity_whitelist:
            errors.append(f"头实体不在白名单中: {triple['head']}")
        
        if triple["tail"] not in self.entity_whitelist:
            errors.append(f"尾实体不在白名单中: {triple['tail']}")
        
        if triple["head_label"] not in self.label_whitelist:
            errors.append(f"头实体标签不在白名单中: {triple['head_label']}")
        
        if triple["tail_label"] not in self.label_whitelist:
            errors.append(f"尾实体标签不在白名单中: {triple['tail_label']}")
        
        if triple["relation"] not in self.relation_whitelist:
            errors.append(f"关系不在白名单中: {triple['relation']}")
        
        return errors
