#!/usr/bin/env python3
"""
约束设置脚本

为Neo4j数据库设置必要的约束。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.neo4j_client import Neo4jClient
from src.data.whitelist_loader import Whitelist<PERSON>oader


def setup_constraints():
    """设置Neo4j约束"""
    print("开始设置Neo4j约束...")
    
    # 创建客户端
    neo4j_client = Neo4jClient()
    whitelist_loader = WhitelistLoader()
    
    # 测试连接
    if not neo4j_client.test_connection():
        print("错误: Neo4j连接失败")
        return False
    
    # 加载标签白名单
    labels = whitelist_loader.load_labels()
    if not labels:
        print("错误: 无法加载标签白名单")
        return False
    
    print(f"为 {len(labels)} 个标签设置约束...")
    
    try:
        with neo4j_client.driver.session() as session:
            success_count = 0
            
            for label in labels:
                try:
                    # 创建name字段的存在性约束
                    constraint_query = f"CREATE CONSTRAINT IF NOT EXISTS FOR (n:`{label}`) REQUIRE n.name IS NOT NULL"
                    session.run(constraint_query)
                    
                    # 创建description字段的存在性约束
                    desc_constraint_query = f"CREATE CONSTRAINT IF NOT EXISTS FOR (n:`{label}`) REQUIRE n.description IS NOT NULL"
                    session.run(desc_constraint_query)
                    
                    # 为重要标签创建唯一性约束
                    if label in ["Function", "Concept", "Parameter"]:
                        unique_constraint_query = f"CREATE CONSTRAINT IF NOT EXISTS FOR (n:`{label}`) REQUIRE n.name IS UNIQUE"
                        session.run(unique_constraint_query)
                        print(f"✓ {label}: 存在性约束 + 唯一性约束")
                    else:
                        print(f"✓ {label}: 存在性约束")
                    
                    success_count += 1
                    
                except Exception as e:
                    print(f"✗ {label}: 约束设置失败 - {e}")
            
            print(f"\n约束设置完成: {success_count}/{len(labels)} 成功")
            
            # 显示所有约束
            print("\n当前数据库约束:")
            try:
                constraints_result = session.run("SHOW CONSTRAINTS")
                constraints = list(constraints_result)
                
                if constraints:
                    for constraint in constraints:
                        print(f"  - {constraint}")
                else:
                    print("  无约束")
                    
            except Exception as e:
                print(f"  获取约束列表失败: {e}")
            
            return success_count > 0
            
    except Exception as e:
        print(f"设置约束时出错: {e}")
        return False
    
    finally:
        neo4j_client.close()


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("用法:")
        print("  python scripts/setup_constraints.py")
        print("")
        print("说明:")
        print("  为Neo4j数据库中的所有标签设置必要的约束")
        print("  包括name和description字段的存在性约束")
        print("  为重要标签(Function, Concept, Parameter)设置name字段的唯一性约束")
        return
    
    try:
        success = setup_constraints()
        
        if success:
            print("\n✅ 约束设置完成！")
        else:
            print("\n❌ 约束设置失败！")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n错误: {e}")


if __name__ == "__main__":
    main()
