// Neo4j 数据清理脚本
// 使用APOC批量删除不在白名单中的节点和关系

// 1. 删除不在实体白名单中的节点
// 注意：entities.csv没有header，第一列是实体名称
LOAD CSV FROM 'file:///whitelists/entities.csv' AS row
WITH collect(row[0]) AS entity_whitelist
CALL apoc.periodic.iterate(
    'MATCH (n) WHERE NOT n.name IN $entities RETURN n',
    'DETACH DELETE n',
    {batchSize: 1000, params: {entities: $entity_whitelist}}
) YIELD batches, total
RETURN "删除无效实体节点" as operation, batches, total;

// 2. 删除不在标签白名单中的节点
LOAD CSV WITH HEADERS FROM 'file:///whitelists/labels.csv' AS row
WITH collect(row.label) AS label_whitelist
CALL apoc.periodic.iterate(
    'MATCH (n) WHERE NONE(label IN labels(n) WHERE label IN $labels) RETURN n',
    'DETACH DELETE n',
    {batchSize: 1000, params: {labels: $label_whitelist}}
) YIELD batches, total
RETURN "删除无效标签节点" as operation, batches, total;

// 3. 删除不在关系白名单中的关系
LOAD CSV WITH HEADERS FROM 'file:///whitelists/relations.csv' AS row
WITH collect(row.relation_type) AS relation_whitelist
CALL apoc.periodic.iterate(
    'MATCH ()-[r]-() WHERE NOT type(r) IN $relations RETURN r',
    'DELETE r',
    {batchSize: 1000, params: {relations: $relation_whitelist}}
) YIELD batches, total
RETURN "删除无效关系" as operation, batches, total;

// 4. 检查清理结果
MATCH (n) 
OPTIONAL MATCH (n)-[r]-()
RETURN count(DISTINCT n) as remaining_nodes, 
       count(DISTINCT r) as remaining_relationships,
       collect(DISTINCT labels(n)) as node_labels,
       collect(DISTINCT type(r)) as relationship_types;

// 5. 验证所有节点都有description属性
MATCH (n) 
WHERE n.description IS NULL 
RETURN count(n) as nodes_without_description, 
       collect(DISTINCT labels(n)) as affected_labels;

// 6. 显示数据统计
MATCH (n)
RETURN labels(n)[0] as label, count(n) as node_count
ORDER BY node_count DESC;
