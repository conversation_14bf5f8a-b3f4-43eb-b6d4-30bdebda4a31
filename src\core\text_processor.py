"""
文本处理模块

负责文本分块、预处理等功能。
"""

from typing import Generator
from ..config.settings import settings


class TextProcessor:
    """文本处理器"""
    
    def __init__(self, max_chars: int = None):
        """
        初始化文本处理器
        
        Args:
            max_chars: 每块最大字符数，默认使用配置值
        """
        self.max_chars = max_chars or settings.max_chunk_chars
    
    def yield_chunks(self, text: str) -> Generator[str, None, None]:
        """
        将文本分割成适合GPT处理的小块
        
        Args:
            text: 输入文本
            
        Yields:
            str: 文本块
        """
        buffer = []
        current_size = 0
        
        for line in text.splitlines():
            line_length = len(line)
            
            # 如果当前行加上缓冲区会超过限制，先输出缓冲区
            if current_size + line_length > self.max_chars and buffer:
                yield "\n".join(buffer)
                buffer = []
                current_size = 0
            
            # 如果单行就超过限制，直接输出
            if line_length > self.max_chars:
                if buffer:
                    yield "\n".join(buffer)
                    buffer = []
                    current_size = 0
                yield line
            else:
                buffer.append(line)
                current_size += line_length
        
        # 输出剩余内容
        if buffer:
            yield "\n".join(buffer)
    
    def preprocess_text(self, text: str) -> str:
        """
        预处理文本
        
        Args:
            text: 原始文本
            
        Returns:
            str: 预处理后的文本
        """
        # 移除多余的空白字符
        text = "\n".join(line.strip() for line in text.splitlines() if line.strip())
        
        # 可以在这里添加更多预处理逻辑
        # 例如：移除特殊字符、标准化编码等
        
        return text
    
    def count_chunks(self, text: str) -> int:
        """
        计算文本会被分割成多少块
        
        Args:
            text: 输入文本
            
        Returns:
            int: 块数
        """
        return sum(1 for _ in self.yield_chunks(text))
    
    def get_text_stats(self, text: str) -> dict:
        """
        获取文本统计信息
        
        Args:
            text: 输入文本
            
        Returns:
            dict: 统计信息
        """
        lines = text.splitlines()
        return {
            "total_chars": len(text),
            "total_lines": len(lines),
            "non_empty_lines": len([line for line in lines if line.strip()]),
            "estimated_chunks": self.count_chunks(text),
            "avg_chars_per_chunk": len(text) / max(1, self.count_chunks(text))
        }
