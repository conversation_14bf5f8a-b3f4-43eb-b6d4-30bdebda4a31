"""
白名单加载模块

负责加载和管理各种白名单数据。
"""

import pandas as pd
from typing import Set, Tuple
from ..config.settings import settings


class WhitelistLoader:
    """白名单加载器"""
    
    def __init__(self):
        """初始化白名单加载器"""
        self._entity_whitelist = None
        self._label_whitelist = None
        self._relation_whitelist = None
    
    def load_entities(self) -> Set[str]:
        """
        加载实体白名单
        
        Returns:
            Set[str]: 实体白名单
        """
        if self._entity_whitelist is None:
            try:
                # 实体白名单（无header）
                entities_df = pd.read_csv(settings.get_whitelist_path("entities"), header=None)
                self._entity_whitelist = set(entities_df[0].dropna().str.strip())
                print(f"加载实体白名单: {len(self._entity_whitelist)} 个实体")
            except Exception as e:
                print(f"加载实体白名单失败: {e}")
                self._entity_whitelist = set()
        
        return self._entity_whitelist
    
    def load_labels(self) -> Set[str]:
        """
        加载标签白名单
        
        Returns:
            Set[str]: 标签白名单
        """
        if self._label_whitelist is None:
            try:
                # 标签白名单
                labels_df = pd.read_csv(settings.get_whitelist_path("labels"))
                self._label_whitelist = set(labels_df["label"].dropna().str.strip())
                print(f"加载标签白名单: {len(self._label_whitelist)} 个标签")
            except Exception as e:
                print(f"加载标签白名单失败: {e}")
                self._label_whitelist = set()
        
        return self._label_whitelist
    
    def load_relations(self) -> Set[str]:
        """
        加载关系白名单
        
        Returns:
            Set[str]: 关系白名单
        """
        if self._relation_whitelist is None:
            try:
                # 关系白名单
                relations_df = pd.read_csv(settings.get_whitelist_path("relations"))
                self._relation_whitelist = set(relations_df["relation_type"].dropna().str.strip())
                print(f"加载关系白名单: {len(self._relation_whitelist)} 个关系")
            except Exception as e:
                print(f"加载关系白名单失败: {e}")
                self._relation_whitelist = set()
        
        return self._relation_whitelist
    
    def load_all(self) -> Tuple[Set[str], Set[str], Set[str]]:
        """
        加载所有白名单
        
        Returns:
            Tuple[Set[str], Set[str], Set[str]]: (实体白名单, 标签白名单, 关系白名单)
        """
        entities = self.load_entities()
        labels = self.load_labels()
        relations = self.load_relations()
        
        print(f"白名单加载完成 - 实体: {len(entities)}, 标签: {len(labels)}, 关系: {len(relations)}")
        
        return entities, labels, relations
    
    def reload(self):
        """重新加载所有白名单"""
        self._entity_whitelist = None
        self._label_whitelist = None
        self._relation_whitelist = None
        print("白名单缓存已清除，下次访问时将重新加载")
    
    def validate_whitelists(self) -> bool:
        """
        验证白名单是否有效
        
        Returns:
            bool: 是否有效
        """
        entities, labels, relations = self.load_all()
        
        if not entities:
            print("错误: 实体白名单为空")
            return False
        
        if not labels:
            print("错误: 标签白名单为空")
            return False
        
        if not relations:
            print("错误: 关系白名单为空")
            return False
        
        print("白名单验证通过")
        return True
