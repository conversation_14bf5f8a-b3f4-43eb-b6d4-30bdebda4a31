# 项目架构说明

## 🏗️ 架构设计原则

本项目重构遵循以下软件架构最佳实践：

### 1. KISS 原则 (Keep It Simple, Stupid)
- **简单明了的模块划分**：每个模块职责单一，功能明确
- **直观的目录结构**：按功能和职责组织文件
- **清晰的接口设计**：模块间接口简单易懂

### 2. 关注点分离 (Separation of Concerns)
- **核心业务逻辑** (`src/core/`)：文本处理、LLM调用、数据库操作
- **数据处理** (`src/data/`)：白名单管理、数据过滤、CSV处理
- **配置管理** (`src/config/`)：设置加载、提示词模板
- **工具函数** (`src/utils/`)：通用辅助功能

### 3. 单一职责原则
每个类和模块都有明确的单一职责：
- `TextProcessor`：专门处理文本分块
- `LLMClient`：专门处理LLM调用
- `Neo4jClient`：专门处理数据库操作
- `DataFilter`：专门处理数据过滤

## 📦 模块详细说明

### 核心模块 (src/core/)

#### TextProcessor
```python
class TextProcessor:
    """文本处理器 - 负责文本分块和预处理"""
    - yield_chunks(): 智能文本分块
    - preprocess_text(): 文本预处理
    - get_text_stats(): 文本统计信息
```

#### LLMClient
```python
class LLMClient:
    """LLM客户端 - 负责与OpenAI API交互"""
    - extract_triples(): 抽取实体关系三元组
    - test_connection(): 测试API连接
    - get_model_info(): 获取模型信息
```

#### Neo4jClient
```python
class Neo4jClient:
    """Neo4j客户端 - 负责数据库操作"""
    - batch_write_triples(): 批量写入三元组
    - test_connection(): 测试数据库连接
    - get_database_stats(): 获取数据库统计
    - clear_database(): 清空数据库
```

#### Pipeline
```python
class Pipeline:
    """主管道 - 协调各组件完成完整流程"""
    - process_text_to_csv(): 文本到CSV
    - process_csv_to_neo4j(): CSV到Neo4j
    - process_text_to_neo4j(): 文本到Neo4j（一步法）
```

### 数据模块 (src/data/)

#### WhitelistLoader
```python
class WhitelistLoader:
    """白名单加载器 - 管理各种白名单数据"""
    - load_entities(): 加载实体白名单
    - load_labels(): 加载标签白名单
    - load_relations(): 加载关系白名单
    - validate_whitelists(): 验证白名单有效性
```

#### DataFilter
```python
class DataFilter:
    """数据过滤器 - 根据白名单过滤数据"""
    - filter_triples(): 过滤三元组
    - get_filter_stats(): 获取过滤统计
    - validate_triple_fields(): 验证三元组字段
```

#### CSVHandler
```python
class CSVHandler:
    """CSV处理器 - 处理CSV文件读写和分析"""
    - save_triples(): 保存三元组到CSV
    - load_triples(): 从CSV加载三元组
    - analyze_triples(): 分析三元组数据
```

### 配置模块 (src/config/)

#### Settings
```python
class Settings:
    """配置管理 - 统一管理所有配置项"""
    - 环境变量加载和验证
    - 默认值设置
    - 路径管理
```

#### PromptTemplates
```python
class PromptTemplates:
    """提示词模板 - 管理LLM提示词"""
    - create_system_prompt(): 创建系统提示词
    - create_extraction_function_def(): 创建函数定义
    - create_user_prompt(): 创建用户提示词
```

## 🔄 数据流程

### 两步法流程
```
文本文件 → TextProcessor → LLMClient → DataFilter → CSVHandler → CSV文件
CSV文件 → CSVHandler → Neo4jClient → Neo4j数据库
```

### 一步法流程
```
文本文件 → Pipeline → (内部调用两步法) → Neo4j数据库
```

## 🎯 设计优势

### 1. 可维护性
- **模块化设计**：每个模块独立，易于维护和测试
- **清晰的依赖关系**：模块间依赖明确，避免循环依赖
- **统一的错误处理**：每个模块都有完善的错误处理

### 2. 可扩展性
- **插件化架构**：可以轻松添加新的处理器或客户端
- **配置驱动**：通过配置文件控制行为，无需修改代码
- **接口标准化**：统一的接口设计便于扩展

### 3. 可测试性
- **单元测试友好**：每个模块都可以独立测试
- **依赖注入**：便于模拟和测试
- **测试工具完善**：提供连接测试、功能测试等工具

### 4. 性能优化
- **资源管理**：合理的连接管理和资源释放
- **批量处理**：支持大数据量的批量处理
- **内存优化**：流式处理减少内存占用

## 🔧 扩展指南

### 添加新的文本处理器
1. 在 `src/core/` 中创建新的处理器类
2. 实现标准接口
3. 在 `Pipeline` 中集成

### 添加新的数据源
1. 在 `src/data/` 中创建新的加载器
2. 实现数据加载接口
3. 更新配置管理

### 添加新的输出格式
1. 在 `src/data/` 中创建新的处理器
2. 实现保存和加载接口
3. 在脚本中添加支持

## 📊 性能监控

### 关键指标
- **文本处理速度**：字符/秒
- **LLM调用延迟**：请求响应时间
- **数据库写入速度**：三元组/秒
- **内存使用量**：峰值内存占用

### 优化建议
- **并行处理**：可以考虑多线程处理文本块
- **缓存机制**：缓存LLM结果减少重复调用
- **批量优化**：调整批量大小优化性能
- **连接池**：使用连接池管理数据库连接

这种架构设计确保了系统的可维护性、可扩展性和性能，同时遵循了软件工程的最佳实践。
