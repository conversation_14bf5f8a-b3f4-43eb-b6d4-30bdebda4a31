#!/usr/bin/env python3
"""
完整管道脚本

从文本文件直接处理到Neo4j数据库的一步式流程。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.pipeline import Pipeline


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python scripts/pipeline.py <文本文件路径> [--clear]")
        print("")
        print("参数:")
        print("  --clear    处理前清空数据库")
        print("")
        print("示例:")
        print("  python scripts/pipeline.py data/samples/python第八章.txt")
        print("  python scripts/pipeline.py data/samples/python第八章.txt --clear")
        print("")
        print("注意:")
        print("  这是一步式处理，需要Neo4j连接正常工作")
        print("  如果连接有问题，建议使用两步法:")
        print("  1. python scripts/extract_to_csv.py <文本文件>")
        print("  2. python scripts/csv_to_neo4j.py <CSV文件>")
        return
    
    # 创建管道
    pipeline = Pipeline()
    
    try:
        text_file = sys.argv[1]
        clear_db = "--clear" in sys.argv
        
        print(f"处理文本文件: {text_file}")
        if clear_db:
            print("⚠️  将清空数据库后处理")
        
        print("\n🚀 开始一步式处理: 文本 -> Neo4j")
        
        # 执行完整流程
        success = pipeline.process_text_to_neo4j(text_file, clear_db)
        
        if success:
            print(f"\n✅ 处理完成！")
            print(f"\n最终数据库状态:")
            pipeline.verify_database()
        else:
            print("\n❌ 处理失败！")
            print("\n建议使用两步法:")
            print("1. python scripts/extract_to_csv.py <文本文件>")
            print("2. python scripts/csv_to_neo4j.py <CSV文件>")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n错误: {e}")
    finally:
        # 清理资源
        pipeline.cleanup()


if __name__ == "__main__":
    main()
