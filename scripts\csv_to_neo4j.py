#!/usr/bin/env python3
"""
CSV到Neo4j脚本

将CSV文件中的三元组导入到Neo4j数据库。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.pipeline import Pipeline


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python scripts/csv_to_neo4j.py <CSV文件路径> [--clear]")
        print("  python scripts/csv_to_neo4j.py verify")
        print("")
        print("参数:")
        print("  --clear    导入前清空数据库")
        print("  verify     验证数据库状态")
        print("")
        print("示例:")
        print("  python scripts/csv_to_neo4j.py data/output/extracted_triples.csv")
        print("  python scripts/csv_to_neo4j.py data/output/extracted_triples.csv --clear")
        print("  python scripts/csv_to_neo4j.py verify")
        return
    
    # 创建管道
    pipeline = Pipeline()
    
    try:
        if sys.argv[1] == "verify":
            # 验证模式
            print("验证Neo4j数据库状态...")
            pipeline.verify_database()
            
        else:
            # 导入模式
            csv_file = sys.argv[1]
            clear_db = "--clear" in sys.argv
            
            print(f"导入CSV文件: {csv_file}")
            if clear_db:
                print("⚠️  将清空数据库后导入")
            
            # 执行导入
            success = pipeline.process_csv_to_neo4j(csv_file, clear_db)
            
            if success:
                print(f"\n✅ 导入完成！")
                print(f"\n数据库状态:")
                pipeline.verify_database()
            else:
                print("\n❌ 导入失败！")
                
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n错误: {e}")
    finally:
        # 清理资源
        pipeline.cleanup()


if __name__ == "__main__":
    main()
