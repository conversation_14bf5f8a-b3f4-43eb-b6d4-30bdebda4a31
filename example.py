#!/usr/bin/env python3
"""
使用示例脚本

演示如何使用重构后的知识图谱构建系统。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from src.core.pipeline import Pipeline


def main():
    """主函数 - 演示系统使用"""
    print("🚀 Neo4j 知识图谱构建系统 - 使用示例")
    print("=" * 50)
    
    # 创建管道实例
    print("1. 初始化管道...")
    try:
        pipeline = Pipeline()
        print("✅ 管道初始化成功")
    except Exception as e:
        print(f"❌ 管道初始化失败: {e}")
        print("\n请检查:")
        print("- .env 文件是否正确配置")
        print("- Neo4j 服务是否运行")
        print("- OpenAI API Key 是否有效")
        return
    
    # 示例文件路径
    sample_file = "data/samples/python第八章.txt"
    
    if not os.path.exists(sample_file):
        print(f"❌ 示例文件不存在: {sample_file}")
        return
    
    print(f"\n2. 处理示例文件: {sample_file}")
    
    # 方法一：两步法（推荐）
    print("\n=== 方法一：两步法（推荐）===")
    
    # 步骤1：抽取到CSV
    print("步骤1: 抽取实体关系到CSV...")
    csv_path = pipeline.process_text_to_csv(sample_file, "example_output.csv")
    
    if csv_path:
        print(f"✅ 抽取完成，输出文件: {csv_path}")
        
        # 分析CSV结果
        print("\n分析抽取结果:")
        pipeline.analyze_csv(csv_path)
        
        # 步骤2：导入到Neo4j
        print("\n步骤2: 导入到Neo4j数据库...")
        
        # 询问是否清空数据库
        response = input("\n是否清空数据库后导入？(y/N): ").strip().lower()
        clear_db = response in ['y', 'yes']
        
        success = pipeline.process_csv_to_neo4j(csv_path, clear_db)
        
        if success:
            print("✅ 导入完成！")
            
            # 验证数据库状态
            print("\n数据库状态:")
            pipeline.verify_database()
        else:
            print("❌ 导入失败")
    else:
        print("❌ 抽取失败")
    
    # 清理资源
    pipeline.cleanup()
    
    print(f"\n{'='*50}")
    print("🎉 示例完成！")
    print("\n接下来你可以:")
    print("1. 在Neo4j Browser中查看知识图谱")
    print("2. 使用自己的文本文件进行处理")
    print("3. 自定义白名单配置")
    print("\n更多使用方法请参考 README.md")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n\n错误: {e}")
        print("请检查配置和环境设置")
