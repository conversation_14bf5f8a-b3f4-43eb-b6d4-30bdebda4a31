"""
配置管理模块

统一管理所有配置项，包括环境变量、默认值等。
"""

import os
from typing import Optional
from dotenv import load_dotenv


class Settings:
    """配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        # 加载环境变量
        load_dotenv(override=True)
        
        # Neo4j配置
        self.neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        self.neo4j_user = os.getenv("NEO4J_USER", "neo4j")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD")
        
        # OpenAI配置
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        
        # 文本处理配置
        self.max_chunk_chars = int(os.getenv("MAX_CHUNK_CHARS", "2800"))
        self.llm_model = os.getenv("LLM_MODEL", "gpt-4o-mini")
        
        # 文件路径配置
        self.whitelists_dir = os.getenv("WHITELISTS_DIR", "data/whitelists")
        self.output_dir = os.getenv("OUTPUT_DIR", "data/output")
        
        # 验证必需的配置
        self._validate_required_settings()
    
    def _validate_required_settings(self):
        """验证必需的配置项"""
        if not self.neo4j_password:
            raise ValueError("NEO4J_PASSWORD环境变量未设置")
        
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY环境变量未设置")
    
    @property
    def neo4j_auth(self) -> tuple[str, str]:
        """获取Neo4j认证信息"""
        return (self.neo4j_user, self.neo4j_password)
    
    def get_whitelist_path(self, whitelist_type: str) -> str:
        """获取白名单文件路径"""
        return os.path.join(self.whitelists_dir, f"{whitelist_type}.csv")
    
    def get_output_path(self, filename: str) -> str:
        """获取输出文件路径"""
        return os.path.join(self.output_dir, filename)


# 全局配置实例
settings = Settings()
