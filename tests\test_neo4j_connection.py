#!/usr/bin/env python3
"""
Neo4j连接测试脚本
详细测试Neo4j连接并输出调试信息
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config.settings import settings
from src.core.neo4j_client import Neo4jClient

def test_connection_detailed():
    """详细测试Neo4j连接"""
    print("=== Neo4j连接详细测试 ===")

    # 1. 显示环境变量
    print("\n1. 环境变量检查:")
    print(f"   NEO4J_URI: {settings.neo4j_uri}")
    print(f"   NEO4J_USER: {settings.neo4j_user}")
    print(f"   NEO4J_PASSWORD: {'*' * len(settings.neo4j_password) if settings.neo4j_password else 'NOT SET'}")

    try:
        settings._validate_required_settings()
        print("✅ 环境变量配置正确")
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        return False

    # 2. 创建Neo4j客户端并测试连接
    print("\n2. 创建Neo4j客户端:")
    try:
        neo4j_client = Neo4jClient()
        print("✓ Neo4j客户端创建成功")
    except Exception as e:
        print(f"❌ Neo4j客户端创建失败: {e}")
        return False

    # 3. 测试连接
    print("\n3. 测试数据库连接:")
    if neo4j_client.test_connection():
        print("✓ 连接测试成功")
    else:
        print("❌ 连接测试失败")
        return False

    # 4. 执行简单查询
    print("\n4. 执行测试查询:")
    try:
        with neo4j_client.driver.session() as session:
            # 测试查询1: 返回简单值
            result = session.run("RETURN 'Hello Neo4j!' as message, datetime() as current_time")
            record = result.single()
            print(f"✓ 测试消息: {record['message']}")
            print(f"✓ 服务器时间: {record['current_time']}")

            # 测试查询2: 数据库信息
            try:
                result = session.run("CALL db.info()")
                info = result.single()
                if info:
                    print(f"✓ 数据库名称: {info.get('name', 'N/A')}")
                    print(f"✓ 数据库ID: {info.get('id', 'N/A')}")
            except Exception as e:
                print(f"⚠️  数据库信息获取失败: {e}")

    except Exception as e:
        print(f"❌ 查询执行失败: {e}")
        neo4j_client.close()
        return False

    # 5. 检查数据库状态
    print("\n5. 数据库状态检查:")
    stats = neo4j_client.get_database_stats()
    if "error" in stats:
        print(f"❌ 状态检查失败: {stats['error']}")
    else:
        print(f"✓ 当前节点数: {stats['node_count']}")
        print(f"✓ 当前关系数: {stats['relationship_count']}")
        print(f"✓ 标签数量: {stats['label_count']}")
        print(f"✓ 关系类型数量: {stats['relationship_type_count']}")

        if stats['labels']:
            print(f"✓ 标签列表: {', '.join(stats['labels'][:5])}")

        if stats['relationship_types']:
            print(f"✓ 关系类型: {', '.join(stats['relationship_types'][:5])}")

    # 6. 测试写入权限
    print("\n6. 测试写入权限:")
    try:
        with neo4j_client.driver.session() as session:
            # 创建测试节点
            session.run("CREATE (test:TestNode {name: 'connection_test', timestamp: datetime()})")
            print("✓ 测试节点创建成功")

            # 查询测试节点
            result = session.run("MATCH (test:TestNode {name: 'connection_test'}) RETURN test.timestamp as timestamp")
            record = result.single()
            if record:
                print(f"✓ 测试节点查询成功: {record['timestamp']}")

            # 删除测试节点
            session.run("MATCH (test:TestNode {name: 'connection_test'}) DELETE test")
            print("✓ 测试节点删除成功")

    except Exception as e:
        print(f"❌ 写入权限测试失败: {e}")
        neo4j_client.close()
        return False

    # 7. 测试APOC可用性
    print("\n7. 测试APOC插件:")
    try:
        with neo4j_client.driver.session() as session:
            result = session.run("RETURN apoc.version() as version")
            record = result.single()
            if record:
                print(f"✓ APOC版本: {record['version']}")
            else:
                print("⚠️  APOC可能未安装或未启用")
    except Exception as e:
        print(f"⚠️  APOC测试失败: {e}")

    # 关闭连接
    neo4j_client.close()
    print("\n✅ 所有连接测试完成！")
    return True

def test_different_configs():
    """测试不同的连接配置"""
    print("\n=== 测试不同连接配置 ===")

    # 使用当前配置进行基本测试
    print("\n测试当前配置:")
    try:
        neo4j_client = Neo4jClient()
        if neo4j_client.test_connection():
            print("✓ 当前配置 - 连接成功")
        else:
            print("❌ 当前配置 - 连接失败")
        neo4j_client.close()
    except Exception as e:
        print(f"❌ 当前配置 - 失败: {e}")

    print("\n建议检查:")
    print("1. Neo4j服务是否运行")
    print("2. 端口7687是否开放")
    print("3. 用户名密码是否正确")
    print("4. 防火墙设置")

def main():
    """主函数"""
    print("Neo4j连接诊断工具")
    print("=" * 50)
    
    # 主要连接测试
    success = test_connection_detailed()
    
    if not success:
        print("\n主要连接测试失败，尝试其他配置...")
        test_different_configs()
    
    print(f"\n{'='*50}")
    print("诊断完成")
    
    if success:
        print("🎉 Neo4j连接正常，可以继续使用系统！")
    else:
        print("❌ Neo4j连接有问题，请检查:")
        print("   1. Neo4j服务是否运行")
        print("   2. 端口7687是否开放")
        print("   3. 用户名密码是否正确")
        print("   4. 数据库是否存在")

if __name__ == "__main__":
    main()
