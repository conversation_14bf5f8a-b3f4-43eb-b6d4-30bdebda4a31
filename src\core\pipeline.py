"""
主管道模块

协调各个组件，实现完整的知识图谱构建流程。
"""

import pandas as pd
from typing import List, Dict, Optional
from tqdm import tqdm

from .text_processor import TextProcessor
from .llm_client import LLMClient
from .neo4j_client import Neo4jClient
from ..data.whitelist_loader import WhitelistLoader
from ..data.filter import DataFilter
from ..data.csv_handler import CSVHandler
from ..utils.helpers import validate_file_path, safe_read_file


class Pipeline:
    """知识图谱构建管道"""
    
    def __init__(self):
        """初始化管道"""
        self.text_processor = TextProcessor()
        self.llm_client = LLMClient()
        self.neo4j_client = Neo4jClient()
        self.whitelist_loader = WhitelistLoader()
        self.csv_handler = CSVHandler()
        
        # 加载白名单
        self.entities, self.labels, self.relations = self.whitelist_loader.load_all()
        self.data_filter = DataFilter(self.entities, self.labels, self.relations)
    
    def process_text_to_csv(self, text_filepath: str, output_csv: str = "extracted_triples.csv") -> str:
        """
        处理文本文件并输出到CSV
        
        Args:
            text_filepath: 输入文本文件路径
            output_csv: 输出CSV文件名
            
        Returns:
            str: 输出文件路径
        """
        print(f"开始处理文件: {text_filepath}")
        
        # 验证输入文件
        if not validate_file_path(text_filepath):
            print(f"错误: 文件不存在 {text_filepath}")
            return ""
        
        # 验证白名单
        if not self.whitelist_loader.validate_whitelists():
            print("错误: 白名单验证失败")
            return ""
        
        # 读取文本
        text = safe_read_file(text_filepath)
        if not text:
            print("错误: 读取文件失败")
            return ""
        
        print(f"文本长度: {len(text)} 字符")
        
        # 获取文本统计信息
        stats = self.text_processor.get_text_stats(text)
        print(f"预计分块数: {stats['estimated_chunks']}")
        
        # 处理文本块
        all_triples = []
        chunks = list(self.text_processor.yield_chunks(text))
        
        print(f"开始处理 {len(chunks)} 个文本块...")
        
        for i, chunk in enumerate(tqdm(chunks, desc="处理文本块")):
            # LLM抽取
            raw_triples = self.llm_client.extract_triples(
                chunk, self.entities, self.labels, self.relations
            )
            
            # 白名单过滤
            filtered_triples = list(self.data_filter.filter_triples(raw_triples))
            all_triples.extend(filtered_triples)
            
            if filtered_triples:
                print(f"块 {i+1}: 抽取 {len(raw_triples)} -> 过滤后 {len(filtered_triples)}")
        
        print(f"总共抽取到 {len(all_triples)} 个有效三元组")
        
        # 保存到CSV
        if all_triples:
            output_path = self.csv_handler.save_triples(all_triples, output_csv)
            return output_path
        else:
            print("警告: 没有抽取到有效三元组")
            return ""
    
    def process_csv_to_neo4j(self, csv_filepath: str, clear_db: bool = False) -> bool:
        """
        处理CSV文件并导入到Neo4j
        
        Args:
            csv_filepath: CSV文件路径
            clear_db: 是否清空数据库
            
        Returns:
            bool: 是否成功
        """
        print(f"开始处理CSV文件: {csv_filepath}")
        
        # 验证文件
        if not validate_file_path(csv_filepath):
            print(f"错误: 文件不存在 {csv_filepath}")
            return False
        
        # 测试Neo4j连接
        if not self.neo4j_client.test_connection():
            print("错误: Neo4j连接失败")
            return False
        
        # 清空数据库（如果需要）
        if clear_db:
            print("清空数据库...")
            self.neo4j_client.clear_database()
        
        # 加载CSV数据
        df = self.csv_handler.load_triples(csv_filepath)
        if df.empty:
            print("错误: CSV文件为空或加载失败")
            return False
        
        # 写入Neo4j
        success = self.neo4j_client.batch_write_triples(df)
        
        if success:
            # 显示统计信息
            stats = self.neo4j_client.get_database_stats()
            print(f"数据库统计: {stats}")
        
        return success
    
    def process_text_to_neo4j(self, text_filepath: str, clear_db: bool = False) -> bool:
        """
        直接从文本文件处理到Neo4j（一步法）
        
        Args:
            text_filepath: 输入文本文件路径
            clear_db: 是否清空数据库
            
        Returns:
            bool: 是否成功
        """
        print("使用一步法：文本 -> Neo4j")
        
        # 先处理到CSV
        temp_csv = "temp_triples.csv"
        csv_path = self.process_text_to_csv(text_filepath, temp_csv)
        
        if not csv_path:
            print("错误: 文本处理失败")
            return False
        
        # 再从CSV导入Neo4j
        success = self.process_csv_to_neo4j(csv_path, clear_db)
        
        return success
    
    def analyze_csv(self, csv_filepath: str):
        """
        分析CSV文件
        
        Args:
            csv_filepath: CSV文件路径
        """
        analysis = self.csv_handler.analyze_triples(csv_filepath)
        self.csv_handler.print_analysis(analysis)
    
    def verify_database(self):
        """验证数据库状态"""
        if not self.neo4j_client.test_connection():
            print("错误: Neo4j连接失败")
            return
        
        stats = self.neo4j_client.get_database_stats()
        
        if "error" in stats:
            print(f"获取数据库统计失败: {stats['error']}")
            return
        
        print("\n=== Neo4j数据库状态 ===")
        print(f"节点数量: {stats['node_count']}")
        print(f"关系数量: {stats['relationship_count']}")
        print(f"标签类型: {stats['label_count']} 个")
        print(f"关系类型: {stats['relationship_type_count']} 个")
        
        if stats['labels']:
            print(f"标签列表: {', '.join(stats['labels'])}")
        
        if stats['relationship_types']:
            print(f"关系类型: {', '.join(stats['relationship_types'])}")
    
    def cleanup(self):
        """清理资源"""
        if self.neo4j_client:
            self.neo4j_client.close()
