"""
LLM客户端模块

负责与OpenAI API交互，进行实体关系抽取。
"""

import json
from typing import List, Dict, Set
from openai import OpenAI
from ..config.settings import settings
from ..config.prompts import PromptTemplates


class LLMClient:
    """LLM客户端"""
    
    def __init__(self):
        """初始化LLM客户端"""
        self.client = OpenAI(api_key=settings.openai_api_key)
        self.model = settings.llm_model
        self.prompt_templates = PromptTemplates()
    
    def extract_triples(self, text_chunk: str, 
                       entity_whitelist: Set[str],
                       label_whitelist: Set[str], 
                       relation_whitelist: Set[str]) -> List[Dict[str, str]]:
        """
        从文本块中抽取实体关系三元组
        
        Args:
            text_chunk: 文本块
            entity_whitelist: 实体白名单
            label_whitelist: 标签白名单
            relation_whitelist: 关系白名单
            
        Returns:
            List[Dict[str, str]]: 抽取的三元组列表
        """
        try:
            # 创建系统提示词
            system_prompt = self.prompt_templates.create_system_prompt(
                entity_whitelist, label_whitelist, relation_whitelist
            )
            
            # 创建用户提示词
            user_prompt = self.prompt_templates.create_user_prompt(text_chunk)
            
            # 创建函数定义
            function_def = self.prompt_templates.create_extraction_function_def()
            
            # 调用GPT
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                functions=[function_def],
                function_call={"name": "add_triples"},
                temperature=0.1
            )
            
            # 解析响应
            return self._parse_response(response)
            
        except Exception as e:
            print(f"LLM抽取失败: {e}")
            return []
    
    def _parse_response(self, response) -> List[Dict[str, str]]:
        """
        解析GPT响应
        
        Args:
            response: GPT响应
            
        Returns:
            List[Dict[str, str]]: 解析的三元组列表
        """
        try:
            # 检查是否有函数调用
            if (response.choices and 
                response.choices[0].message.function_call and
                response.choices[0].message.function_call.arguments):
                
                # 解析函数调用参数
                arguments = json.loads(response.choices[0].message.function_call.arguments)
                
                if "triples" in arguments:
                    return arguments["triples"]
                else:
                    print("警告: 函数调用结果中没有triples字段")
                    return []
            else:
                print("警告: GPT没有返回函数调用结果")
                return []
                
        except json.JSONDecodeError as e:
            print(f"解析GPT响应失败: {e}")
            return []
        except Exception as e:
            print(f"处理GPT响应时出错: {e}")
            return []
    
    def test_connection(self) -> bool:
        """
        测试LLM连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            print("LLM连接测试成功")
            return True
        except Exception as e:
            print(f"LLM连接测试失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, str]:
        """
        获取模型信息
        
        Returns:
            Dict[str, str]: 模型信息
        """
        return {
            "model": self.model,
            "api_key_set": bool(settings.openai_api_key),
            "api_key_prefix": settings.openai_api_key[:10] + "..." if settings.openai_api_key else "未设置"
        }
