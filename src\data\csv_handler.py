"""
CSV处理模块

负责CSV文件的读取、写入和分析。
"""

import pandas as pd
from typing import List, Dict, Any
from ..config.settings import settings


class CSVHandler:
    """CSV处理器"""
    
    def __init__(self):
        """初始化CSV处理器"""
        pass
    
    def save_triples(self, triples: List[Dict[str, str]], filename: str = "extracted_triples.csv") -> str:
        """
        保存三元组到CSV文件
        
        Args:
            triples: 三元组列表
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        if not triples:
            print("警告: 没有三元组数据需要保存")
            return ""
        
        try:
            # 转换为DataFrame
            df = pd.DataFrame(triples)
            
            # 确保列顺序
            columns = ["head", "head_label", "head_desc", "relation", "tail", "tail_label", "tail_desc"]
            df = df.reindex(columns=columns, fill_value="")
            
            # 保存文件
            filepath = settings.get_output_path(filename)
            df.to_csv(filepath, index=False, encoding="utf-8")
            
            print(f"三元组已保存到: {filepath}")
            print(f"共保存 {len(triples)} 个三元组")
            
            return filepath
            
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return ""
    
    def load_triples(self, filepath: str) -> pd.DataFrame:
        """
        从CSV文件加载三元组
        
        Args:
            filepath: 文件路径
            
        Returns:
            pd.DataFrame: 三元组DataFrame
        """
        try:
            df = pd.read_csv(filepath, encoding="utf-8")
            print(f"从 {filepath} 加载了 {len(df)} 个三元组")
            return df
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            return pd.DataFrame()
    
    def analyze_triples(self, filepath: str) -> Dict[str, Any]:
        """
        分析三元组CSV文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            df = self.load_triples(filepath)
            
            if df.empty:
                return {"error": "文件为空或加载失败"}
            
            # 基本统计
            stats = {
                "total_triples": len(df),
                "unique_entities": len(set(df["head"].tolist() + df["tail"].tolist())),
                "unique_head_entities": df["head"].nunique(),
                "unique_tail_entities": df["tail"].nunique(),
                "unique_relations": df["relation"].nunique(),
                "unique_labels": len(set(df["head_label"].tolist() + df["tail_label"].tolist()))
            }
            
            # 标签分布
            head_labels = df["head_label"].value_counts().to_dict()
            tail_labels = df["tail_label"].value_counts().to_dict()
            all_labels = df["head_label"].tolist() + df["tail_label"].tolist()
            label_distribution = pd.Series(all_labels).value_counts().to_dict()
            
            # 关系分布
            relation_distribution = df["relation"].value_counts().to_dict()
            
            # 实体分布
            all_entities = df["head"].tolist() + df["tail"].tolist()
            entity_distribution = pd.Series(all_entities).value_counts().head(10).to_dict()
            
            # 数据质量检查
            quality_issues = []
            
            # 检查空值
            for col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    quality_issues.append(f"{col}列有{null_count}个空值")
            
            # 检查空字符串
            for col in ["head", "tail", "relation"]:
                if col in df.columns:
                    empty_count = (df[col] == "").sum()
                    if empty_count > 0:
                        quality_issues.append(f"{col}列有{empty_count}个空字符串")
            
            return {
                "basic_stats": stats,
                "label_distribution": label_distribution,
                "relation_distribution": relation_distribution,
                "top_entities": entity_distribution,
                "head_label_distribution": head_labels,
                "tail_label_distribution": tail_labels,
                "quality_issues": quality_issues
            }
            
        except Exception as e:
            return {"error": f"分析文件失败: {e}"}
    
    def print_analysis(self, analysis: Dict[str, Any]):
        """
        打印分析结果
        
        Args:
            analysis: 分析结果
        """
        if "error" in analysis:
            print(f"分析失败: {analysis['error']}")
            return
        
        print("\n=== 三元组分析报告 ===")
        
        # 基本统计
        stats = analysis["basic_stats"]
        print(f"\n📊 基本统计:")
        print(f"  总三元组数: {stats['total_triples']}")
        print(f"  唯一实体数: {stats['unique_entities']}")
        print(f"  唯一关系数: {stats['unique_relations']}")
        print(f"  唯一标签数: {stats['unique_labels']}")
        
        # 标签分布
        print(f"\n🏷️ 标签分布:")
        for label, count in list(analysis["label_distribution"].items())[:5]:
            print(f"  {label}: {count}")
        
        # 关系分布
        print(f"\n🔗 关系分布:")
        for relation, count in list(analysis["relation_distribution"].items())[:5]:
            print(f"  {relation}: {count}")
        
        # 数据质量
        if analysis["quality_issues"]:
            print(f"\n⚠️ 数据质量问题:")
            for issue in analysis["quality_issues"]:
                print(f"  {issue}")
        else:
            print(f"\n✅ 数据质量良好，无发现问题")
