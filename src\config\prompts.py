"""
LLM提示词模板管理

统一管理所有LLM提示词模板。
"""

from typing import Dict, Any, Set


class PromptTemplates:
    """提示词模板管理类"""
    
    @staticmethod
    def create_system_prompt(entity_whitelist: Set[str], 
                           label_whitelist: Set[str], 
                           relation_whitelist: Set[str]) -> str:
        """
        创建系统提示词
        
        Args:
            entity_whitelist: 实体白名单
            label_whitelist: 标签白名单
            relation_whitelist: 关系白名单
            
        Returns:
            str: 系统提示词
        """
        return f"""你是一个信息抽取助手，专门从Python编程教材中抽取实体关系。

重要规则：
1. 只抽取在实体白名单中的实体：{sorted(list(entity_whitelist))}
2. 标签只能从以下列表选择：{sorted(list(label_whitelist))}
3. 关系只能从以下列表选择：{sorted(list(relation_whitelist))}
4. 如果实体不在白名单中，完全忽略它
5. 为每个保留的实体自动生成：
   - 合适的标签（从标签白名单中选择）
   - 简洁的中文描述（不超过20字）

输出要求：
- 使用提供的函数格式返回结果
- 确保所有字段都有值
- 描述要准确、简洁
- 关系要符合语义逻辑

示例输出格式：
头实体: "函数", 标签: "Function", 描述: "带名字的代码块"
关系: "is_a_type_of"  
尾实体: "代码块", 标签: "CodeBlock", 描述: "用于完成具体工作的代码块"
"""

    @staticmethod
    def create_extraction_function_def() -> Dict[str, Any]:
        """
        创建GPT函数调用定义
        
        Returns:
            dict: 函数定义
        """
        return {
            "name": "add_triples",
            "description": "提取并返回实体关系三元组",
            "parameters": {
                "type": "object",
                "properties": {
                    "triples": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "head": {"type": "string", "description": "头实体名称"},
                                "head_label": {"type": "string", "description": "头实体标签"},
                                "head_desc": {"type": "string", "description": "头实体描述"},
                                "relation": {"type": "string", "description": "关系类型"},
                                "tail": {"type": "string", "description": "尾实体名称"},
                                "tail_label": {"type": "string", "description": "尾实体标签"},
                                "tail_desc": {"type": "string", "description": "尾实体描述"}
                            },
                            "required": ["head", "head_label", "relation", "tail", "tail_label"]
                        }
                    }
                },
                "required": ["triples"]
            }
        }

    @staticmethod
    def create_user_prompt(text_chunk: str) -> str:
        """
        创建用户提示词
        
        Args:
            text_chunk: 文本块
            
        Returns:
            str: 用户提示词
        """
        return f"""请从以下Python编程教材文本中抽取实体关系三元组：

{text_chunk}

请严格按照白名单要求，只抽取白名单中的实体，并为每个实体生成合适的标签和描述。"""
