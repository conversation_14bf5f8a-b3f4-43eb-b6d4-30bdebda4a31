"""
Neo4j客户端模块

负责与Neo4j数据库交互，包括连接、写入、查询等操作。
"""

import pandas as pd
from typing import Dict, List, Any
from neo4j import GraphDatabase
from ..config.settings import settings


class Neo4jClient:
    """Neo4j客户端"""
    
    def __init__(self):
        """初始化Neo4j客户端"""
        self.driver = None
        self._connect()
    
    def _connect(self):
        """建立Neo4j连接"""
        try:
            self.driver = GraphDatabase.driver(
                settings.neo4j_uri, 
                auth=settings.neo4j_auth
            )
            print("Neo4j连接建立成功")
        except Exception as e:
            print(f"Neo4j连接失败: {e}")
            self.driver = None
    
    def test_connection(self) -> bool:
        """
        测试Neo4j连接
        
        Returns:
            bool: 连接是否成功
        """
        if not self.driver:
            return False
        
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                print(f"Neo4j连接测试成功，返回值: {test_value}")
                return True
        except Exception as e:
            print(f"Neo4j连接测试失败: {e}")
            return False
    
    def batch_write_triples(self, triples_df: pd.DataFrame) -> bool:
        """
        批量写入三元组到Neo4j
        
        Args:
            triples_df: 包含三元组的DataFrame
            
        Returns:
            bool: 写入是否成功
        """
        if not self.driver:
            print("错误: Neo4j连接未建立")
            return False
        
        if triples_df.empty:
            print("没有数据需要写入")
            return True
        
        try:
            with self.driver.session() as session:
                # 按照头标签、尾标签、关系类型分组，以便使用动态标签
                for (head_label, tail_label, relation), group in triples_df.groupby(
                        ["head_label", "tail_label", "relation"]):
                    
                    self._write_triple_group(session, head_label, tail_label, relation, group)
            
            print(f"成功写入 {len(triples_df)} 个三元组到Neo4j")
            return True
            
        except Exception as e:
            print(f"批量写入失败: {e}")
            return False
    
    def _write_triple_group(self, session, head_label: str, tail_label: str, 
                           relation: str, group: pd.DataFrame):
        """
        写入一组三元组
        
        Args:
            session: Neo4j会话
            head_label: 头实体标签
            tail_label: 尾实体标签
            relation: 关系类型
            group: 三元组组
        """
        try:
            # 构建动态Cypher查询
            query = f"""
            UNWIND $rows AS row
            MERGE (h:`{head_label}` {{name: row.head}})
              ON CREATE SET h.description = row.head_desc
              ON MATCH SET h.description = COALESCE(h.description, row.head_desc)
            MERGE (t:`{tail_label}` {{name: row.tail}})
              ON CREATE SET t.description = row.tail_desc
              ON MATCH SET t.description = COALESCE(t.description, row.tail_desc)
            MERGE (h)-[r:`{relation}`]->(t)
            """
            
            # 转换为记录格式
            records = group.to_dict("records")
            
            # 执行批量写入
            result = session.run(query, rows=records)
            result.consume()  # 确保查询执行完成
            
            print(f"✓ 写入 {len(records)} 个三元组: {head_label}-[{relation}]->{tail_label}")
            
        except Exception as e:
            print(f"✗ 写入失败 {head_label}-[{relation}]->{tail_label}: {e}")
    
    def clear_database(self) -> bool:
        """
        清空数据库
        
        Returns:
            bool: 清空是否成功
        """
        if not self.driver:
            print("错误: Neo4j连接未建立")
            return False
        
        try:
            with self.driver.session() as session:
                # 删除所有节点和关系
                session.run("MATCH (n) DETACH DELETE n")
                print("数据库已清空")
                return True
        except Exception as e:
            print(f"清空数据库失败: {e}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not self.driver:
            return {"error": "Neo4j连接未建立"}
        
        try:
            with self.driver.session() as session:
                # 获取节点数量
                node_count = session.run("MATCH (n) RETURN count(n) as count").single()["count"]
                
                # 获取关系数量
                rel_count = session.run("MATCH ()-[r]->() RETURN count(r) as count").single()["count"]
                
                # 获取标签统计
                labels_result = session.run("CALL db.labels() YIELD label RETURN collect(label) as labels")
                labels = labels_result.single()["labels"]
                
                # 获取关系类型统计
                rel_types_result = session.run("CALL db.relationshipTypes() YIELD relationshipType RETURN collect(relationshipType) as types")
                rel_types = rel_types_result.single()["types"]
                
                return {
                    "node_count": node_count,
                    "relationship_count": rel_count,
                    "labels": labels,
                    "relationship_types": rel_types,
                    "label_count": len(labels),
                    "relationship_type_count": len(rel_types)
                }
        except Exception as e:
            return {"error": f"获取统计信息失败: {e}"}
    
    def close(self):
        """关闭连接"""
        if self.driver:
            self.driver.close()
            print("Neo4j连接已关闭")
    
    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.close()
