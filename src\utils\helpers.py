"""
通用工具函数

包含各种辅助函数和工具。
"""

import os
from typing import Optional


def validate_file_path(filepath: str) -> bool:
    """
    验证文件路径是否存在
    
    Args:
        filepath: 文件路径
        
    Returns:
        bool: 文件是否存在
    """
    return os.path.exists(filepath) and os.path.isfile(filepath)


def safe_read_file(filepath: str, encoding: str = "utf-8") -> Optional[str]:
    """
    安全读取文件内容
    
    Args:
        filepath: 文件路径
        encoding: 文件编码
        
    Returns:
        Optional[str]: 文件内容，失败时返回None
    """
    try:
        with open(filepath, "r", encoding=encoding) as f:
            return f.read()
    except Exception as e:
        print(f"读取文件失败 {filepath}: {e}")
        return None


def ensure_directory(dirpath: str) -> bool:
    """
    确保目录存在，不存在则创建
    
    Args:
        dirpath: 目录路径
        
    Returns:
        bool: 目录是否存在或创建成功
    """
    try:
        if not os.path.exists(dirpath):
            os.makedirs(dirpath, exist_ok=True)
            print(f"创建目录: {dirpath}")
        return True
    except Exception as e:
        print(f"创建目录失败 {dirpath}: {e}")
        return False


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化的大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def get_file_info(filepath: str) -> dict:
    """
    获取文件信息
    
    Args:
        filepath: 文件路径
        
    Returns:
        dict: 文件信息
    """
    try:
        stat = os.stat(filepath)
        return {
            "path": filepath,
            "size_bytes": stat.st_size,
            "size_formatted": format_file_size(stat.st_size),
            "modified_time": stat.st_mtime,
            "exists": True
        }
    except Exception as e:
        return {
            "path": filepath,
            "error": str(e),
            "exists": False
        }


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    截断文本
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        str: 截断后的文本
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def print_progress(current: int, total: int, prefix: str = "进度"):
    """
    打印进度信息
    
    Args:
        current: 当前进度
        total: 总数
        prefix: 前缀文本
    """
    percentage = (current / total) * 100 if total > 0 else 0
    print(f"{prefix}: {current}/{total} ({percentage:.1f}%)")


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 默认值
        
    Returns:
        float: 除法结果
    """
    return numerator / denominator if denominator != 0 else default
